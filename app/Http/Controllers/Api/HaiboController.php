<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\HaiboService;
use App\Services\RateLimiterService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class HaiboController extends Controller
{
    protected HaiboService $haiboService;
    protected RateLimiterService $rateLimiterService;

    public function __construct(HaiboService $haiboService, RateLimiterService $rateLimiterService)
    {
        $this->haiboService = $haiboService;
        $this->rateLimiterService = $rateLimiterService;
    }

    /**
     * 创建/修改配送商门店接口
     *
     * POST /api/haibo/store
     * Content-Type: application/x-www-form-urlencoded
     */
    public function createOrUpdateStore(Request $request)
    {
        try {
            // 限流检查：2次/秒
            $rateLimitKey = 'haibo:store:rate_limit';
            $maxRequests = 2; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {
                Log::channel('haibo')->warning('海博创建/修改配送商门店请求触发限流', [
                    'data' => $request->all(),
                    'current_count' => $this->rateLimiterService->getCurrentCount($rateLimitKey, $windowSeconds)
                ]);

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }

            // 记录海博请求信息
            Log::channel('haibo')->info('海博创建/修改配送商门店请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数 - 根据海博接口规范
            $validator = Validator::make($request->all(), [
                'contactPhone' => 'required',
                'shopLng' => 'required|numeric',
                'shopLat' => 'required|numeric',
            ], [
                'contactPhone.required' => '门店联系人电话不能为空',
                'shopLng.required' => '门店经度不能为空',
                'shopLat.required' => '门店纬度不能为空',
                'shopLng.numeric' => '门店经度必须为数字',
                'shopLat.numeric' => '门店纬度必须为数字',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博请求参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => 1, // 海博规范：非0表示失败
                    'message' => '系统异常',
                    'data' => null
                ]);
            }

            // 检查运力覆盖
            $data = $request->all();
            $point = ["lng" => $data["shopLng"]/1000000, "lat" => $data["shopLat"]/1000000];
            $site = app(\App\Services\CommonService::class)->getPointSite($point);

            if (!$site) {
                Log::channel('haibo')->warning('海博门店创建失败：该地址所在区域暂未开通服务', [
                    'longitude' => $data["shopLng"],
                    'latitude' => $data["shopLat"],
                    'data' => $data
                ]);

                return response()->json([
                    'code' => 10, // 不存在运力时返回 code 10
                    'message' => '该地区暂无运力',
                    'data' => null
                ]);
            }

            // 调用服务处理业务逻辑
            $result = $this->haiboService->createOrUpdateStore($request->all());

            Log::channel('haibo')->info('海博门店操作成功', $result);

            // 按照海博接口规范返回响应
            return response()->json([
                'code' => 0, // 海博规范：0表示成功
                'message' => '成功',
                'data' => [
                    'carrierShopId' => $result['data']['carrier_shop_id'],
                    "serviceCodes"=>["HBT_YQS_1"]
                ]
            ]);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博门店操作异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => 1, // 海博规范：非0表示失败
                'message' => '系统异常',
                'data' => null
            ]);
        }
    }

    /**
     * 询价接口 - 获取预估配送费、配送时间等信息
     *
     * POST /api/haibo/valuating
     * Content-Type: application/json
     */
    public function valuating(Request $request)
    {
        try {
            // 限流检查：2次/秒
            $rateLimitKey = 'haibo:valuating:rate_limit';
            $maxRequests = 2; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {
                Log::channel('haibo')->warning('海博询价请求触发限流', [
                    'data' => $request->all(),
                    'current_count' => $this->rateLimiterService->getCurrentCount($rateLimitKey, $windowSeconds)
                ]);

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }

            // 记录海博询价请求信息
            Log::channel('haibo')->info('海博询价请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required', // 平台订单号
                'recipientLng' => 'required', // 收件人经度
                'recipientLat' => 'required',// 收件人纬度
                'senderLng' => 'required', // 发件人经度
                'senderLat' => 'required',// 发件人纬度
                "carrierShopId"=>"required",
            ], [
                'orderId.required' => '海博平台订单号不能为空',
                'recipientLng.required' => '收件人经度不能为空',
                'recipientLat.required' => '收件人纬度不能为空',
                'senderLng.required' => '发件人经度不能为空',
                'senderLat.required' => '发件人纬度不能为空',
                "carrierShopId.required"=>"门店id不能为空",
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博询价参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => HaiboService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 检查发件人和收件人地址的运力覆盖
            $data = $request->all();

            // 转换经纬度格式（海博传入的是整数，需要转换为小数）
            $senderPoint = ["lng" => $data["senderLng"]/1000000, "lat" => $data["senderLat"]/1000000];
            $recipientPoint = ["lng" => $data["recipientLng"]/1000000, "lat" => $data["recipientLat"]/1000000];

            // 检查发件人地址运力覆盖
            $senderSite = app(\App\Services\CommonService::class)->getPointSite($senderPoint);
            if (!$senderSite) {
                Log::channel('haibo')->warning('海博询价失败：发件人地址所在区域暂无运力', [
                    'senderLng' => $data["senderLng"],
                    'senderLat' => $data["senderLat"],
                    'data' => $data
                ]);

                return response()->json([
                    'code' => 10, // 不存在运力时返回 code 10
                    'message' => '该地区暂无运力',
                    'data' => null
                ]);
            }

            // 检查收件人地址运力覆盖
            $recipientSite = app(\App\Services\CommonService::class)->getPointSite($recipientPoint);
            if (!$recipientSite) {
                Log::channel('haibo')->warning('海博询价失败：收件人地址所在区域暂无运力', [
                    'recipientLng' => $data["recipientLng"],
                    'recipientLat' => $data["recipientLat"],
                    'data' => $data
                ]);

                return response()->json([
                    'code' => 10, // 不存在运力时返回 code 10
                    'message' => '该地区暂无运力',
                    'data' => null
                ]);
            }

            // 按照 maiyatian 的实现模式处理询价逻辑
            $result = $this->haiboService->valuatingWithOrderLookup($request->all());

            Log::channel('haibo')->info('海博询价处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博询价异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => HaiboService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 发单接口 - 创建配送订单
     *
     * POST /api/haibo/send
     * Content-Type: application/json
     */
    public function send(Request $request)
    {
        try {
            // 限流检查：2次/秒
            $rateLimitKey = 'haibo:send:rate_limit';
            $maxRequests = 2; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {
                Log::channel('haibo')->warning('海博发单请求触发限流', [
                    'data' => $request->all(),
                    'current_count' => $this->rateLimiterService->getCurrentCount($rateLimitKey, $windowSeconds)
                ]);

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }

            // 记录海博发单请求信息
            Log::channel('haibo')->info('海博发单请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required',
                'recipientLng' => 'required|integer',
                'recipientLat' => 'required|integer',
                'senderLng' => 'required|integer',
                'senderLat' => 'required|integer',
            ], [
                'orderId.required' => '海博平台订单号不能为空',
                'recipientLng.required' => '收件人经度不能为空',
                'recipientLat.required' => '收件人纬度不能为空',
                'senderLng.required' => '发件人经度不能为空',
                'senderLat.required' => '发件人纬度不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博发单参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => HaiboService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 检查订单是否已存在
            $existingOrder = \App\Models\O2oErrandOrder::where('out_order_no', $request->input('orderId'))
                ->where('app_key', \App\Models\O2oErrandOrder::APP_KEY_HB)
                ->first();

            if ($existingOrder) {
                Log::channel('haibo')->info('海博发单成功（订单已存在）', [
                    'order_id' => $request->input('orderId'),
                    'existing_order_no' => $existingOrder->order_no,
                    'existing_order_id' => $existingOrder->id
                ]);

                // 使用已存在的订单格式化返回数据
                $result = $this->haiboService->formatOrderResultData($existingOrder);

                return response()->json([
                    'code' => HaiboService::RESULT_SUCCESS,
                    'message' => '成功',
                    'data' => $result
                ]);
            }

            // 检查发件人和收件人地址的运力覆盖
            $data = $request->all();

            // 转换经纬度格式（海博传入的是整数，需要转换为小数）
            $senderPoint = ["lng" => $data["senderLng"]/1000000, "lat" => $data["senderLat"]/1000000];
            $recipientPoint = ["lng" => $data["recipientLng"]/1000000, "lat" => $data["recipientLat"]/1000000];

            // 检查发件人地址运力覆盖
            $senderSite = app(\App\Services\CommonService::class)->getPointSite($senderPoint);
            if (!$senderSite) {
                Log::channel('haibo')->warning('海博发单失败：发件人地址所在区域暂无运力', [
                    'senderLng' => $data["senderLng"],
                    'senderLat' => $data["senderLat"],
                    'data' => $data
                ]);

                return response()->json([
                    'code' => 10, // 不存在运力时返回 code 10
                    'message' => '该地区暂无运力',
                    'data' => null
                ]);
            }

            // 检查收件人地址运力覆盖
            $recipientSite = app(\App\Services\CommonService::class)->getPointSite($recipientPoint);
            if (!$recipientSite) {
                Log::channel('haibo')->warning('海博发单失败：收件人地址所在区域暂无运力', [
                    'recipientLng' => $data["recipientLng"],
                    'recipientLat' => $data["recipientLat"],
                    'data' => $data
                ]);

                return response()->json([
                    'code' => 10, // 不存在运力时返回 code 10
                    'message' => '该地区暂无运力',
                    'data' => null
                ]);
            }

            // 调用服务处理发单逻辑
            $result = $this->haiboService->send($request->all());

            Log::channel('haibo')->info('海博发单处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博发单异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => HaiboService::RESULT_SYSTEM_ERROR,
                'message' => '系统异常',
                'data' => null
            ]);
        }
    }

    /**
     * 取消订单接口
     *
     * POST /api/haibo/cancel
     * Content-Type: application/json
     */
    public function cancel(Request $request)
    {
        try {
            // 限流检查：2次/秒
            $rateLimitKey = 'haibo:cancel:rate_limit';
            $maxRequests = 2; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {
                Log::channel('haibo')->warning('海博取消订单请求触发限流', [
                    'data' => $request->all(),
                    'current_count' => $this->rateLimiterService->getCurrentCount($rateLimitKey, $windowSeconds)
                ]);

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }

            // 记录海博取消订单请求信息
            Log::channel('haibo')->info('海博取消订单请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required',
                'cancelReasonCode' => 'required',
                'cancelReasonDesc' => 'required',
            ], [
                'orderId.required' => '海博平台订单号不能为空',
                'cancelReasonCode.required' => '取消原因code不能为空',
                'cancelReasonDesc.required' => '取消原因说明不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博取消订单参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => HaiboService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 调用服务处理取消订单逻辑
            $result = $this->haiboService->cancelOrder($request->all());

            Log::channel('haibo')->info('海博取消订单处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博取消订单异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => HaiboService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 订单详情接口
     *
     * POST /api/haibo/order-detail
     * Content-Type: application/json
     */
    public function orderDetail(Request $request)
    {
        try {
            // 限流检查：2次/秒
            $rateLimitKey = 'haibo:order_detail:rate_limit';
            $maxRequests = 2; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {
                Log::channel('haibo')->warning('海博订单详情请求触发限流', [
                    'data' => $request->all(),
                    'current_count' => $this->rateLimiterService->getCurrentCount($rateLimitKey, $windowSeconds)
                ]);

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }

            // 记录海博订单详情请求信息
            Log::channel('haibo')->info('海博订单详情请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required',
            ], [
                'orderId.required' => '海博平台订单号不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博订单详情参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => HaiboService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 调用服务处理订单详情查询逻辑
            $result = $this->haiboService->getOrderDetail($request->all());

            Log::channel('haibo')->info('海博订单详情处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博订单详情异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => HaiboService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 骑手/司机经纬度查询接口
     *
     * POST /api/haibo/rider-location
     * Content-Type: application/json
     */
    public function riderLocation(Request $request)
    {
        try {
            // 限流检查：2次/秒
            $rateLimitKey = 'haibo:rider_location:rate_limit';
            $maxRequests = 2; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {
                Log::channel('haibo')->warning('海博骑手位置查询请求触发限流', [
                    'data' => $request->all(),
                    'current_count' => $this->rateLimiterService->getCurrentCount($rateLimitKey, $windowSeconds)
                ]);

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }

            // 记录海博骑手位置查询请求信息
            Log::channel('haibo')->info('海博骑手位置查询请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required',
            ], [
                'orderId.required' => '海博平台订单号不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博骑手位置查询参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => HaiboService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 调用服务处理骑手位置查询逻辑
            $result = $this->haiboService->getRiderLocation($request->all());

            Log::channel('haibo')->info('海博骑手位置查询处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博骑手位置查询异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => HaiboService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

    /**
     * 添加小费接口
     *
     * POST /api/haibo/add-tip
     * Content-Type: application/json
     */
    public function addTip(Request $request)
    {
        try {
            // 限流检查：2次/秒
            $rateLimitKey = 'haibo:add_tip:rate_limit';
            $maxRequests = 2; // 最大请求数
            $windowSeconds = 1; // 时间窗口：1秒

            if (!$this->rateLimiterService->checkRateLimit($rateLimitKey, $maxRequests, $windowSeconds)) {
                Log::channel('haibo')->warning('海博添加小费请求触发限流', [
                    'data' => $request->all(),
                    'current_count' => $this->rateLimiterService->getCurrentCount($rateLimitKey, $windowSeconds)
                ]);

                return response()->json([
                    'code' => 5, // 限流时返回 code 5
                    'message' => '请求过于频繁，请稍后再试',
                    'data' => null
                ]);
            }

            // 记录海博添加小费请求信息
            Log::channel('haibo')->info('海博添加小费请求', [
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'orderId' => 'required|string',
                'tipFee' => 'required|numeric',
            ], [
                'orderId.required' => '海博平台订单号不能为空',
                'tipFee.required' => '小费金额不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博添加小费参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => HaiboService::RESULT_PARAM_ERROR,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ]);
            }

            // 调用服务处理添加小费逻辑
            $result = $this->haiboService->addTip($request->all());

            Log::channel('haibo')->info('海博添加小费处理完成', $result);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博添加小费异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => HaiboService::RESULT_SYSTEM_ERROR,
                'message' => '系统错误: ' . $e->getMessage(),
                'data' => null
            ]);
        }
    }

}
